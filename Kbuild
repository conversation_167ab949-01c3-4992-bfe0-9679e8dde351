# Select one of the targets to build
# CONFIG_NVMEVIRT_NVM := y
# CONFIG_NVMEVIRT_SSD := y
# CONFIG_NVMEVIRT_ZNS := y
#CONFIG_NVMEVIRT_KV := y
CONFIG_NVMEVIRT_FDP := y

obj-m   := nvmev.o
nvmev-objs := src/main.o src/pci.o src/admin.o src/io.o src/dma.o
ccflags-y += -Wno-unused-variable -Wno-unused-function
ccflags-y += -I$(src)/include

ccflags-$(CONFIG_NVMEVIRT_NVM) += -DBASE_SSD=INTEL_OPTANE
ccflags-$(CONFIG_NVMEVIRT_NVM) += -DCONFIG_NVMEVIRT_NVM
nvmev-$(CONFIG_NVMEVIRT_NVM) += src/simple_ftl.o

ccflags-$(CONFIG_NVMEVIRT_SSD) += -DBASE_SSD=SAMSUNG_970PRO
ccflags-$(CONFIG_NVMEVIRT_SSD) += -DCONFIG_NVMEVIRT_SSD
nvmev-$(CONFIG_NVMEVIRT_SSD) += src/ssd.o src/conv_ftl.o include/pqueue/pqueue.o src/channel_model.o

ccflags-$(CONFIG_NVMEVIRT_ZNS) += -DBASE_SSD=WD_ZN540
#ccflags-$(CONFIG_NVMEVIRT_ZNS) += -DBASE_SSD=ZNS_PROTOTYPE
ccflags-$(CONFIG_NVMEVIRT_ZNS) += -Wno-implicit-fallthrough
ccflags-$(CONFIG_NVMEVIRT_ZNS) += -DCONFIG_NVMEVIRT_ZNS
nvmev-$(CONFIG_NVMEVIRT_ZNS) += src/ssd.o src/zns_ftl.o src/zns_read_write.o src/zns_mgmt_send.o src/zns_mgmt_recv.o src/channel_model.o

ccflags-$(CONFIG_NVMEVIRT_KV) += -DBASE_SSD=KV_PROTOTYPE
ccflags-$(CONFIG_NVMEVIRT_KV) += -DCONFIG_NVMEVIRT_KV
nvmev-$(CONFIG_NVMEVIRT_KV) += src/kv_ftl.o src/append_only.o src/bitmap.o

ccflags-$(CONFIG_NVMEVIRT_FDP) += -DBASE_SSD=FDP_PROTOTYPE
ccflags-$(CONFIG_NVMEVIRT_FDP) += -DCONFIG_NVMEVIRT_FDP_ENABLED
ccflags-$(CONFIG_NVMEVIRT_FDP) += -DCONFIG_NVMEVIRT_FDP
nvmev-$(CONFIG_NVMEVIRT_FDP) += src/ssd.o src/fdp_ftl.o include/pqueue/pqueue.o src/channel_model.o
